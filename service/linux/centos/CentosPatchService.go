package centos

import (
	"compress/gzip"
	"crypto/tls"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"os"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model/linux/centos"
	centosrepo "patch-central-repo/repository/linux/centos"
	"patch-central-repo/rest"
	service2 "patch-central-repo/service"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

type CentosPatchService struct {
	Repository *centosrepo.CentosPatchRepository
}

var (
	// CentOS mirror URLs for different versions and repositories
	CENTOS_MIRROR_URL                    = "http://mirror.centos.org/centos/%s/%s/%s/repodata/repomd.xml"
	CENTOS_PRIMARY_XML_URL               = "http://mirror.centos.org/centos/%s/%s/%s/repodata/%s"
	SUPPORTED_CENTOS_VERSIONS            = []string{"7", "8", "9"}
	SUPPORTED_CENTOS_REPOSITORIES        = []string{"os", "updates", "extras", "centosplus"}
	SUPPORTED_CENTOS_ARCHITECTURES       = []string{"x86_64", "i386"}
	CENTOS_STREAM_MIRROR_URL             = "http://mirror.stream.centos.org/%s-stream/BaseOS/%s/os/repodata/repomd.xml"
	CENTOS_STREAM_PRIMARY_XML_URL        = "http://mirror.stream.centos.org/%s-stream/BaseOS/%s/os/repodata/%s"
	SUPPORTED_CENTOS_STREAM_VERSIONS     = []string{"8", "9"}
)

func NewCentosPatchService() *CentosPatchService {
	return &CentosPatchService{
		Repository: centosrepo.NewCentosPatchRepository(),
	}
}

// RepoMd represents the repomd.xml structure
type RepoMd struct {
	XMLName xml.Name `xml:"repomd"`
	Data    []Data   `xml:"data"`
}

type Data struct {
	Type     string   `xml:"type,attr"`
	Location Location `xml:"location"`
	Checksum Checksum `xml:"checksum"`
}

type Location struct {
	Href string `xml:"href,attr"`
}

type Checksum struct {
	Type  string `xml:"type,attr"`
	Value string `xml:",chardata"`
}

// Metadata represents the primary.xml structure
type Metadata struct {
	XMLName  xml.Name  `xml:"metadata"`
	Packages []Package `xml:"package"`
}

type Package struct {
	Type        string      `xml:"type,attr"`
	Name        string      `xml:"name"`
	Arch        string      `xml:"arch"`
	Version     Version     `xml:"version"`
	Checksum    Checksum    `xml:"checksum"`
	Summary     string      `xml:"summary"`
	Description string      `xml:"description"`
	Packager    string      `xml:"packager"`
	URL         string      `xml:"url"`
	Time        Time        `xml:"time"`
	Size        Size        `xml:"size"`
	Location    Location    `xml:"location"`
	Format      Format      `xml:"format"`
}

type Version struct {
	Epoch   string `xml:"epoch,attr"`
	Ver     string `xml:"ver,attr"`
	Rel     string `xml:"rel,attr"`
}

type Time struct {
	File  string `xml:"file,attr"`
	Build string `xml:"build,attr"`
}

type Size struct {
	Package   string `xml:"package,attr"`
	Installed string `xml:"installed,attr"`
	Archive   string `xml:"archive,attr"`
}

type Format struct {
	License      string       `xml:"license"`
	Vendor       string       `xml:"vendor"`
	Group        string       `xml:"group"`
	BuildHost    string       `xml:"buildhost"`
	SourceRpm    string       `xml:"sourcerpm"`
	HeaderRange  HeaderRange  `xml:"header-range"`
	Provides     Provides     `xml:"provides"`
	Requires     Requires     `xml:"requires"`
	Conflicts    Conflicts    `xml:"conflicts"`
	Obsoletes    Obsoletes    `xml:"obsoletes"`
}

type HeaderRange struct {
	Start string `xml:"start,attr"`
	End   string `xml:"end,attr"`
}

type Provides struct {
	Entries []Entry `xml:"entry"`
}

type Requires struct {
	Entries []Entry `xml:"entry"`
}

type Conflicts struct {
	Entries []Entry `xml:"entry"`
}

type Obsoletes struct {
	Entries []Entry `xml:"entry"`
}

type Entry struct {
	Name    string `xml:"name,attr"`
	Flags   string `xml:"flags,attr"`
	Epoch   string `xml:"epoch,attr"`
	Ver     string `xml:"ver,attr"`
	Rel     string `xml:"rel,attr"`
	PreReq  string `xml:"pre,attr"`
}

func (service CentosPatchService) convertToModel(patchInfo centos.CentosPatch) *centos.CentosPatch {
	return &centos.CentosPatch{
		BaseEntityModel:   patchInfo.BaseEntityModel,
		UUID:              patchInfo.UUID,
		OsVersion:         patchInfo.OsVersion,
		Channel:           patchInfo.Channel,
		Repo:              patchInfo.Repo,
		PackageName:       patchInfo.PackageName,
		Arch:              patchInfo.Arch,
		Version:           patchInfo.Version,
		Release:           patchInfo.Release,
		Epoch:             patchInfo.Epoch,
		Summary:           patchInfo.Summary,
		Description:       patchInfo.Description,
		License:           patchInfo.License,
		Group:             patchInfo.Group,
		Vendor:            patchInfo.Vendor,
		Packager:          patchInfo.Packager,
		BuildHost:         patchInfo.BuildHost,
		SourceRpm:         patchInfo.SourceRpm,
		Requires:          patchInfo.Requires,
		Provides:          patchInfo.Provides,
		Conflicts:         patchInfo.Conflicts,
		Obsoletes:         patchInfo.Obsoletes,
		FileName:          patchInfo.FileName,
		DownloadUrl:       patchInfo.DownloadUrl,
		Size:              patchInfo.Size,
		InstalledSize:     patchInfo.InstalledSize,
		Checksum:          patchInfo.Checksum,
		ChecksumType:      patchInfo.ChecksumType,
		PkgAndVersion:     patchInfo.PkgAndVersion,
		PkgNameWithDistro: patchInfo.PkgNameWithDistro,
		Downloadable:      patchInfo.Downloadable,
		ReleaseDate:       patchInfo.ReleaseDate,
		BuildTime:         patchInfo.BuildTime,
	}
}

func (service CentosPatchService) SyncCentosMirrorPkg() {
	logger.ServiceLogger.Info("Process started to Sync CentOS Mirror Packages")
	
	// Sync regular CentOS versions
	for _, version := range SUPPORTED_CENTOS_VERSIONS {
		for _, repo := range SUPPORTED_CENTOS_REPOSITORIES {
			for _, arch := range SUPPORTED_CENTOS_ARCHITECTURES {
				time.Sleep(5 * time.Second)
				logger.ServiceLogger.Info(fmt.Sprintf("[SyncCentosMirrorPkg] Processing CentOS %s %s %s", version, repo, arch))
				err := service.processCentosRepository(version, repo, arch, false)
				if err != nil {
					logger.ServiceLogger.Error(fmt.Sprintf("[SyncCentosMirrorPkg] Error processing CentOS %s %s %s: %v", version, repo, arch, err))
				}
			}
		}
	}
	
	// Sync CentOS Stream versions
	for _, version := range SUPPORTED_CENTOS_STREAM_VERSIONS {
		for _, arch := range SUPPORTED_CENTOS_ARCHITECTURES {
			time.Sleep(5 * time.Second)
			logger.ServiceLogger.Info(fmt.Sprintf("[SyncCentosMirrorPkg] Processing CentOS Stream %s %s", version, arch))
			err := service.processCentosRepository(version, "BaseOS", arch, true)
			if err != nil {
				logger.ServiceLogger.Error(fmt.Sprintf("[SyncCentosMirrorPkg] Error processing CentOS Stream %s %s: %v", version, arch, err))
			}
		}
	}
	
	logger.ServiceLogger.Info("Process completed to Sync CentOS Mirror Packages")
}

func (service CentosPatchService) processCentosRepository(version, repo, arch string, isStream bool) error {
	var repomdURL string
	var baseURL string

	if isStream {
		repomdURL = fmt.Sprintf(CENTOS_STREAM_MIRROR_URL, version, arch)
		baseURL = fmt.Sprintf("http://mirror.stream.centos.org/%s-stream/BaseOS/%s/os/", version, arch)
	} else {
		repomdURL = fmt.Sprintf(CENTOS_MIRROR_URL, version, repo, arch)
		baseURL = fmt.Sprintf("http://mirror.centos.org/centos/%s/%s/%s/", version, repo, arch)
	}

	// Get repomd.xml to find primary.xml location
	primaryXMLPath, err := service.getPrimaryXMLPath(repomdURL)
	if err != nil {
		return fmt.Errorf("error getting primary XML path: %w", err)
	}

	var primaryXMLURL string
	if isStream {
		primaryXMLURL = fmt.Sprintf(CENTOS_STREAM_PRIMARY_XML_URL, version, arch, primaryXMLPath)
	} else {
		primaryXMLURL = fmt.Sprintf(CENTOS_PRIMARY_XML_URL, version, repo, arch, primaryXMLPath)
	}

	// Download and process primary.xml
	err = service.downloadAndProcessPrimaryXML(primaryXMLURL, version, repo, arch, baseURL, isStream)
	if err != nil {
		return fmt.Errorf("error processing primary XML: %w", err)
	}

	return nil
}

func (service CentosPatchService) getPrimaryXMLPath(repomdURL string) (string, error) {
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	resp, err := client.Get(repomdURL)
	if err != nil {
		return "", fmt.Errorf("error fetching repomd.xml: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP error %d when fetching repomd.xml", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("error reading repomd.xml: %w", err)
	}

	var repomd RepoMd
	err = xml.Unmarshal(body, &repomd)
	if err != nil {
		return "", fmt.Errorf("error parsing repomd.xml: %w", err)
	}

	// Find primary data location
	for _, data := range repomd.Data {
		if data.Type == "primary" {
			return data.Location.Href, nil
		}
	}

	return "", fmt.Errorf("primary data not found in repomd.xml")
}

func (service CentosPatchService) downloadAndProcessPrimaryXML(primaryXMLURL, version, repo, arch, baseURL string, isStream bool) error {
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	resp, err := client.Get(primaryXMLURL)
	if err != nil {
		return fmt.Errorf("error fetching primary XML: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP error %d when fetching primary XML", resp.StatusCode)
	}

	// Handle gzipped content
	var reader io.Reader
	if strings.HasSuffix(primaryXMLURL, ".gz") {
		gzReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return fmt.Errorf("error creating gzip reader: %w", err)
		}
		defer gzReader.Close()
		reader = gzReader
	} else {
		reader = resp.Body
	}

	// Parse XML
	decoder := xml.NewDecoder(reader)

	for {
		token, err := decoder.Token()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("error parsing XML: %w", err)
		}

		switch se := token.(type) {
		case xml.StartElement:
			if se.Name.Local == "package" {
				var pkg Package
				err = decoder.DecodeElement(&pkg, &se)
				if err != nil {
					logger.ServiceLogger.Error("Error decoding package: ", err)
					continue
				}

				// Process the package
				service.processPackage(pkg, version, repo, arch, baseURL, isStream)
			}
		}
	}

	return nil
}

func (service CentosPatchService) processPackage(pkg Package, version, repo, arch, baseURL string, isStream bool) {
	if pkg.Name == "" || pkg.Arch == "" {
		return
	}

	// Create unique UUID for the package
	repoName := repo
	if isStream {
		repoName = "stream-" + repo
	}
	uuid := fmt.Sprintf("%s_%s_%s_%s_%s_%s", version, repoName, arch, pkg.Name, pkg.Version.Ver, pkg.Version.Rel)

	// Check if package already exists
	existingPkg, _ := service.Repository.FindByUUID(uuid)
	if existingPkg.Id != 0 {
		return
	}

	// Parse package size
	packageSize, _ := strconv.ParseInt(pkg.Size.Package, 10, 64)
	installedSize, _ := strconv.ParseInt(pkg.Size.Installed, 10, 64)

	// Parse build time
	buildTime, _ := strconv.ParseInt(pkg.Time.Build, 10, 64)

	// Create download URL
	downloadURL := baseURL + pkg.Location.Href

	// Convert dependencies to strings
	requires := service.convertDependenciesToString(pkg.Format.Requires.Entries)
	provides := service.convertDependenciesToString(pkg.Format.Provides.Entries)
	conflicts := service.convertDependenciesToString(pkg.Format.Conflicts.Entries)
	obsoletes := service.convertDependenciesToString(pkg.Format.Obsoletes.Entries)

	// Create CentOS patch object
	centosPatch := centos.CentosPatch{
		UUID:              uuid,
		OsVersion:         version,
		Channel:           repo,
		Repo:              repoName,
		PackageName:       pkg.Name,
		Arch:              pkg.Arch,
		Version:           pkg.Version.Ver,
		Release:           pkg.Version.Rel,
		Epoch:             pkg.Version.Epoch,
		Summary:           pkg.Summary,
		Description:       pkg.Description,
		License:           pkg.Format.License,
		Group:             pkg.Format.Group,
		Vendor:            pkg.Format.Vendor,
		Packager:          pkg.Packager,
		BuildHost:         pkg.Format.BuildHost,
		SourceRpm:         pkg.Format.SourceRpm,
		Requires:          requires,
		Provides:          provides,
		Conflicts:         conflicts,
		Obsoletes:         obsoletes,
		FileName:          filepath.Base(pkg.Location.Href),
		DownloadUrl:       downloadURL,
		Size:              packageSize,
		InstalledSize:     installedSize,
		Checksum:          pkg.Checksum.Value,
		ChecksumType:      pkg.Checksum.Type,
		PkgAndVersion:     fmt.Sprintf("%s@_@%s-%s", pkg.Name, pkg.Version.Ver, pkg.Version.Rel),
		PkgNameWithDistro: fmt.Sprintf("%s/centos%s", pkg.Name, version),
		Downloadable:      true,
		ReleaseDate:       time.Now().UnixMilli(),
		BuildTime:         buildTime * 1000, // Convert to milliseconds
		CreatedTime:       time.Now().UnixMilli(),
		UpdatedTime:       time.Now().UnixMilli(),
	}

	// Save to database
	_, err := service.Repository.Create(&centosPatch)
	if err != nil {
		logger.ServiceLogger.Error("Error creating CentOS patch: ", err)
	}
}

func (service CentosPatchService) convertDependenciesToString(entries []Entry) string {
	if len(entries) == 0 {
		return ""
	}

	var deps []string
	for _, entry := range entries {
		dep := entry.Name
		if entry.Flags != "" && entry.Ver != "" {
			dep += fmt.Sprintf(" %s %s", entry.Flags, entry.Ver)
			if entry.Rel != "" {
				dep += "-" + entry.Rel
			}
		}
		deps = append(deps, dep)
	}

	return strings.Join(deps, ", ")
}

func (service CentosPatchService) GetAllCentosPatches(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareQueryFromSearchFilter(filter, common.CENTOS.String()+"_patch", true)
	var responsePage rest.ListResponseRest
	var packagePageList []centos.CentosPatch
	var err error
	count := service.Repository.Count(countQuery)
	if count > 0 {
		searchQuery := rest.PrepareQueryFromSearchFilter(filter, common.CENTOS.String()+"_patch", false)
		packagePageList, err = service.Repository.GetAllCentosPatches(searchQuery)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = packagePageList
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service CentosPatchService) Create(patchInfo centos.CentosPatch) (int64, common.CustomError) {
	logger.ServiceLogger.Info("Process started to create CentOS patch")

	patch := service.convertToModel(patchInfo)
	patch.CreatedTime = time.Now().UnixMilli()
	patch.UpdatedTime = time.Now().UnixMilli()
	id, err := service.Repository.Create(patch)
	if err != nil {
		logger.ServiceLogger.Error("Error while creating CentOS patch: ", err.Error())
		return 0, common.CustomError{Message: err.Error()}
	}
	return id, common.CustomError{}
}
