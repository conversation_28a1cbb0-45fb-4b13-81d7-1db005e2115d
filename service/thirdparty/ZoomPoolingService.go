package thirdparty

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/thirdparty"
	"time"
)

var zoomMetaData = map[string]interface{}{
	"uuid": "j1k2l3m4-h5i6-7890-ghij-123456789jkl",
	"templateFileNameMap": map[string]interface{}{
		"x64": "ZOOM_X64.xml",
	},
}

type ZoomDownloadVO struct {
	ZoomX64 ZoomX64Info `json:"zoomX64"`
}

type ZoomX64Info struct {
	Version        string `json:"version"`
	DisplayVersion string `json:"displayVersion"`
	PackageName    string `json:"packageName"`
	ArchType       string `json:"archType"`
}

type ZoomAPIResult struct {
	DownloadVO ZoomDownloadVO `json:"downloadVO"`
}

type ZoomAPIResponse struct {
	Status       bool          `json:"status"`
	ErrorCode    int           `json:"errorCode"`
	ErrorMessage interface{}   `json:"errorMessage"`
	Result       ZoomAPIResult `json:"result"`
}

type ZoomPoolingService struct {
	ThirdPartyPackageService
}

func (c ZoomPoolingService) Name() string {
	return "ZoomPoolingService"
}

func init() {
	RegisterCollector(ZoomPoolingService{})
}

func NewZoomPoolingService() *ZoomPoolingService {
	return &ZoomPoolingService{}
}

func (service ZoomPoolingService) ExecuteSync() {
	logger.ServiceLogger.Debug("Fetching Zoom data for Windows platform")

	// Fetch latest patch data
	err := service.fetchLatestPatchData()
	if err != nil {
		logger.ServiceLogger.Error("Error while fetching Zoom data: ", err)
		return
	}

	// Create allpatchlist.txt and 7z files
	service.CreateRequiredFiles(common.ZOOM)
}

func (service ZoomPoolingService) fetchLatestPatchData() error {
	// Get version and download info from Zoom API
	zoomInfo, err := service.getLatestVersionFromAPI()
	if err != nil {
		return fmt.Errorf("error getting latest version: %w", err)
	}

	logger.ServiceLogger.Debug("Found Zoom version: ", zoomInfo.Version)

	// Construct download URL - use MSI package for IT deployment
	downloadURL := "https://zoom.us/client/latest/ZoomInstallerFull.msi"

	// Create release package if required
	err = service.createReleasePackageIfRequired(zoomInfo.Version, downloadURL, common.X64)
	if err != nil {
		return fmt.Errorf("error creating release package: %w", err)
	}

	return nil
}

func (service ZoomPoolingService) getLatestVersionFromAPI() (*ZoomX64Info, error) {
	url := "https://zoom.us/rest/download?os=win"

	// Create HTTP client with TLS config
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making HTTP request: %w", err)
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error("Error closing HTTP response body: ", err)
		}
	}(resp.Body)

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	// Parse JSON response
	var apiResponse ZoomAPIResponse
	err = json.Unmarshal(body, &apiResponse)
	if err != nil {
		return nil, fmt.Errorf("error parsing JSON response: %w", err)
	}

	if !apiResponse.Status {
		return nil, fmt.Errorf("API returned error status: %d", apiResponse.ErrorCode)
	}

	if apiResponse.Result.DownloadVO.ZoomX64.Version == "" {
		return nil, fmt.Errorf("could not extract version from Zoom API")
	}

	return &apiResponse.Result.DownloadVO.ZoomX64, nil
}

func (service ZoomPoolingService) createReleasePackageIfRequired(version, downloadURL string, osArch common.OsArchitecture) error {
	thirdPartyRepo := NewThirdPartyPackageService().Repository

	// Check if package already exists for this version and architecture
	existingPkg, _ := thirdPartyRepo.GetPkgByPlatformOsArchApplication(int(osArch), int(common.Windows), int(common.ZOOM))
	if existingPkg.Id > 0 && existingPkg.Version == version {
		logger.ServiceLogger.Debug("Data already exists for Zoom version ", version, " arch ", osArch.String())
		return nil
	}

	logger.ServiceLogger.Debug("New Version found for Zoom ", version, " creating package data")

	// Get file size and headers
	headers := common.GetHeadersFromUrl(downloadURL)
	if headers == nil {
		return fmt.Errorf("could not get headers for download URL: %s", downloadURL)
	}

	fileSize := common.GetFileSizeFromUrl(downloadURL)
	fileName := fmt.Sprintf("ZoomInstallerFull_%s.msi", version)

	// Use current time as release date since Zoom doesn't provide this info in the direct download
	releaseTime := time.Now()

	// Delete existing package if it exists
	if existingPkg.Id > 0 {
		DeleteXmlForWindows(existingPkg.Uuid, common.ZOOM)
		_, _ = thirdPartyRepo.DeletePatch(existingPkg)
	}

	// Create new package
	uuid := fmt.Sprintf("%s_%s_%s", common.ZOOM.String(), version, osArch.String())

	pkg := thirdparty.ThirdPartyPackage{
		BaseEntityModel: model.BaseEntityModel{
			Name: "Zoom",
		},
		Description:      "Zoom is a video conferencing platform that provides HD video, audio, collaboration, chat, and webinar capabilities across mobile devices, desktops, telephones, and room systems.",
		Version:          version,
		Os:               common.Windows,
		Arch:             osArch,
		LanguageCode:     "en-US",
		LatestPackageUrl: downloadURL,
		Publisher:        "Zoom Video Communications, Inc.",
		SupportUrl:       "https://support.zoom.com/",
		ReleaseNote:      "https://support.zoom.com/hc/en/article?id=zm_kb&sysparm_article=KB0061222",
		ReleaseDate:      releaseTime.UnixMilli(),
		Application:      common.ZOOM,
		Uuid:             uuid,
	}

	// Create file data
	fileData := model.FileData{
		FileName:    fileName,
		DownloadUrl: downloadURL,
		Size:        fileSize,
		ReleaseDate: releaseTime.UnixMilli(),
	}
	pkg.PkgFileData = []model.FileData{fileData}

	// Generate install and uninstall commands
	installCmd, uninstallCmd := GenerateInstallUninstallCommands(common.ZOOM)
	pkg.InstallCommand = installCmd
	pkg.UnInstallCommand = uninstallCmd

	pkg.CreatedTime = time.Now().UnixMilli()
	pkg.UpdatedTime = time.Now().UnixMilli()

	_, err := thirdPartyRepo.Create(&pkg)
	if err != nil {
		return fmt.Errorf("error creating Zoom package: %w", err)
	}

	// Generate XML file for Windows
	GenerateXmlForWindows(zoomMetaData, version, osArch, uuid, common.ZOOM)

	logger.ServiceLogger.Debug("Package data created successfully for Zoom version ", version)
	return nil
}
