package thirdparty

import (
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/thirdparty"
	"regexp"
	"time"
)

var vlcMetaData = map[string]interface{}{
	"uuid": "g1h2i3j4-e5f6-7890-defg-123456789ghi",
	"templateFileNameMap": map[string]interface{}{
		"x64": "VLC_X64.xml",
	},
}

type VLCPoolingService struct {
	ThirdPartyPackageService
}

func (c VLCPoolingService) Name() string {
	return "VLCPoolingService"
}

func init() {
	RegisterCollector(VLCPoolingService{})
}

func NewVLCPoolingService() *VLCPoolingService {
	return &VLCPoolingService{}
}

func (service VLCPoolingService) ExecuteSync() {
	logger.ServiceLogger.Debug("Fetching VLC data for Windows platform")

	// Fetch latest patch data
	err := service.fetchLatestPatchData()
	if err != nil {
		logger.ServiceLogger.Error("Error while fetching VLC data: ", err)
		return
	}

	// Create allpatchlist.txt and 7z files
	service.CreateRequiredFiles(common.VLC)
}

func (service VLCPoolingService) fetchLatestPatchData() error {
	url := "https://get.videolan.org/vlc/last/win64/"

	// Create HTTP client with TLS config
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error making HTTP request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %w", err)
	}

	// Parse the directory listing to find the latest version
	version, downloadURL, err := service.parseVLCVersion(string(body))
	if err != nil {
		return fmt.Errorf("error parsing VLC version: %w", err)
	}

	logger.ServiceLogger.Debug("Found VLC version: ", version)

	// Create release package if required
	err = service.createReleasePackageIfRequired(version, downloadURL, common.X64)
	if err != nil {
		return fmt.Errorf("error creating release package: %w", err)
	}

	return nil
}

func (service VLCPoolingService) parseVLCVersion(htmlContent string) (string, string, error) {
	// Look for the .exe file in the directory listing
	// Pattern: vlc-X.X.X-win64.exe
	re := regexp.MustCompile(`vlc-(\d+\.\d+\.\d+)-win64\.exe`)
	matches := re.FindStringSubmatch(htmlContent)

	if len(matches) < 2 {
		return "", "", fmt.Errorf("could not find VLC version in directory listing")
	}

	version := matches[1]
	downloadURL := fmt.Sprintf("https://get.videolan.org/vlc/last/win64/vlc-%s-win64.exe", version)

	return version, downloadURL, nil
}

func (service VLCPoolingService) createReleasePackageIfRequired(version, downloadURL string, osArch common.OsArchitecture) error {
	thirdPartyRepo := NewThirdPartyPackageService().Repository

	// Check if package already exists for this version and architecture
	existingPkg, _ := thirdPartyRepo.GetPkgByPlatformOsArchApplication(int(osArch), int(common.Windows), int(common.VLC))
	if existingPkg.Id > 0 && existingPkg.Version == version {
		logger.ServiceLogger.Debug("Data already exists for VLC version ", version, " arch ", osArch.String())
		return nil
	}

	logger.ServiceLogger.Debug("New Version found for VLC ", version, " creating package data")

	// Get file size and headers
	headers := common.GetHeadersFromUrl(downloadURL)
	if headers == nil {
		return fmt.Errorf("could not get headers for download URL: %s", downloadURL)
	}

	fileSize := common.GetFileSizeFromUrl(downloadURL)
	fileName := fmt.Sprintf("vlc-%s-win64.exe", version)

	// Use current time as release date since VLC doesn't provide this info
	releaseTime := time.Now()

	// Delete existing package if it exists
	if existingPkg.Id > 0 {
		DeleteXmlForWindows(existingPkg.Uuid, common.VLC)
		_, _ = thirdPartyRepo.DeletePatch(existingPkg)
	}

	// Create new package
	uuid := fmt.Sprintf("%s_%s_%s", common.VLC.String(), version, osArch.String())

	pkg := thirdparty.ThirdPartyPackage{
		BaseEntityModel: model.BaseEntityModel{
			Name: "VLC Media Player",
		},
		Description:      "VLC is a free and open source cross-platform multimedia player and framework that plays most multimedia files as well as DVDs, Audio CDs, VCDs, and various streaming protocols.",
		Version:          version,
		Os:               common.Windows,
		Arch:             osArch,
		LanguageCode:     "en-US",
		LatestPackageUrl: downloadURL,
		Publisher:        "VideoLAN",
		SupportUrl:       "https://www.videolan.org/support/",
		ReleaseNote:      "https://www.videolan.org/developers/vlc-branch/NEWS",
		ReleaseDate:      releaseTime.UnixMilli(),
		Application:      common.VLC,
		Uuid:             uuid,
	}

	// Create file data
	fileData := model.FileData{
		FileName:    fileName,
		DownloadUrl: downloadURL,
		Size:        fileSize,
		ReleaseDate: releaseTime.UnixMilli(),
	}
	pkg.PkgFileData = []model.FileData{fileData}

	// Generate install and uninstall commands
	installCmd, uninstallCmd := GenerateInstallUninstallCommands(common.VLC)
	pkg.InstallCommand = installCmd
	pkg.UnInstallCommand = uninstallCmd

	pkg.CreatedTime = time.Now().UnixMilli()
	pkg.UpdatedTime = time.Now().UnixMilli()

	_, err := thirdPartyRepo.Create(&pkg)
	if err != nil {
		return fmt.Errorf("error creating VLC package: %w", err)
	}

	// Generate XML file for Windows
	GenerateXmlForWindows(vlcMetaData, version, osArch, uuid, common.VLC)

	logger.ServiceLogger.Debug("Package data created successfully for VLC version ", version)
	return nil
}
