package thirdparty

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/thirdparty"
	"time"
)

var dbeaverMetaData = map[string]interface{}{
	"uuid": "f1g2h3i4-d5e6-7890-cdef-123456789fgh",
	"templateFileNameMap": map[string]interface{}{
		"x64": "DBEAVER_X64.xml",
	},
}

type DBeaverPoolingService struct {
	ThirdPartyPackageService
}

func (c DBeaverPoolingService) Name() string {
	return "DBeaverPoolingService"
}

func init() {
	RegisterCollector(DBeaverPoolingService{})
}

func NewDBeaverPoolingService() *DBeaverPoolingService {
	return &DBeaverPoolingService{}
}

func (service DBeaverPoolingService) ExecuteSync() {
	logger.ServiceLogger.Debug("Fetching DBeaver data for Windows platform")

	// Fetch latest patch data
	err := service.fetchLatestPatchData()
	if err != nil {
		logger.ServiceLogger.Error("Error while fetching DBeaver data: ", err)
		return
	}

	// Create allpatchlist.txt and 7z files
	service.CreateRequiredFiles(common.DBEAVER)
}

type DBeaverGitHubRelease struct {
	TagName     string         `json:"tag_name"`
	Name        string         `json:"name"`
	PublishedAt string         `json:"published_at"`
	Assets      []DBeaverAsset `json:"assets"`
}

type DBeaverAsset struct {
	Name               string `json:"name"`
	BrowserDownloadURL string `json:"browser_download_url"`
	Size               int64  `json:"size"`
}

func (service DBeaverPoolingService) fetchLatestPatchData() error {
	url := "https://api.github.com/repos/dbeaver/dbeaver/releases/latest"

	// Create HTTP client with TLS config
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error making HTTP request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %w", err)
	}

	var release DBeaverGitHubRelease
	err = json.Unmarshal(body, &release)
	if err != nil {
		return fmt.Errorf("error parsing JSON response: %w", err)
	}

	logger.ServiceLogger.Debug("Found DBeaver version: ", release.TagName)

	// Create release package if required
	err = service.createReleasePackageIfRequired(release, common.X64)
	if err != nil {
		return fmt.Errorf("error creating release package: %w", err)
	}

	return nil
}

func (service DBeaverPoolingService) createReleasePackageIfRequired(release DBeaverGitHubRelease, osArch common.OsArchitecture) error {
	thirdPartyRepo := NewThirdPartyPackageService().Repository

	// Check if package already exists for this version and architecture
	existingPkg, _ := thirdPartyRepo.GetPkgByPlatformOsArchApplication(int(osArch), int(common.Windows), int(common.DBEAVER))
	if existingPkg.Id > 0 && existingPkg.Version == release.TagName {
		logger.ServiceLogger.Debug("Data already exists for DBeaver version ", release.TagName, " arch ", osArch.String())
		return nil
	}

	logger.ServiceLogger.Debug("New Version found for DBeaver ", release.TagName, " creating package data")

	// Fixed download URL as specified in requirements
	downloadURL := "https://dbeaver.io/files/dbeaver-ce-latest-x86_64-setup.exe"

	// Get file size and headers
	headers := common.GetHeadersFromUrl(downloadURL)
	if headers == nil {
		return fmt.Errorf("could not get headers for download URL: %s", downloadURL)
	}

	fileSize := common.GetFileSizeFromUrl(downloadURL)
	fileName := "dbeaver-ce-" + release.TagName + "-x86_64-setup.exe"

	// Parse release date from published_at
	releaseTime, err := time.Parse(time.RFC3339, release.PublishedAt)
	if err != nil {
		logger.ServiceLogger.Error("Error parsing release date, using current time: ", err)
		releaseTime = time.Now()
	}

	// Delete existing package if it exists
	if existingPkg.Id > 0 {
		DeleteXmlForWindows(existingPkg.Uuid, common.DBEAVER)
		_, _ = thirdPartyRepo.DeletePatch(existingPkg)
	}

	// Create new package
	uuid := fmt.Sprintf("%s_%s_%s", common.DBEAVER.String(), release.TagName, osArch.String())

	pkg := thirdparty.ThirdPartyPackage{
		BaseEntityModel: model.BaseEntityModel{
			Name: "DBeaver Community",
		},
		Description:      "DBeaver Community is a free cross-platform database tool for developers, database administrators, analysts, and everyone working with data. It supports all popular SQL databases like MySQL, MariaDB, PostgreSQL, SQLite, Apache Family, and more.",
		Version:          release.TagName,
		Os:               common.Windows,
		Arch:             osArch,
		LanguageCode:     "en-US",
		LatestPackageUrl: downloadURL,
		Publisher:        "DBeaver Corp",
		SupportUrl:       "https://dbeaver.io/support/",
		ReleaseNote:      "https://github.com/dbeaver/dbeaver/releases",
		ReleaseDate:      releaseTime.UnixMilli(),
		Application:      common.DBEAVER,
		Uuid:             uuid,
	}

	// Create file data
	fileData := model.FileData{
		FileName:    fileName,
		DownloadUrl: downloadURL,
		Size:        fileSize,
		ReleaseDate: releaseTime.UnixMilli(),
	}
	pkg.PkgFileData = []model.FileData{fileData}

	// Generate install and uninstall commands
	installCmd, uninstallCmd := GenerateInstallUninstallCommands(common.DBEAVER)
	pkg.InstallCommand = installCmd
	pkg.UnInstallCommand = uninstallCmd

	pkg.CreatedTime = time.Now().UnixMilli()
	pkg.UpdatedTime = time.Now().UnixMilli()

	_, err = thirdPartyRepo.Create(&pkg)
	if err != nil {
		return fmt.Errorf("error creating DBeaver package: %w", err)
	}

	// Generate XML file for Windows
	GenerateXmlForWindows(dbeaverMetaData, release.TagName, osArch, uuid, common.DBEAVER)

	logger.ServiceLogger.Debug("Package data created successfully for DBeaver version ", release.TagName)
	return nil
}
