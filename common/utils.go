package common

import (
	"bufio"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"golang.org/x/exp/maps"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"path/filepath"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"unicode"
)

var htmlTagRegex = regexp.MustCompile(`<.*?>`)

func GetEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if len(value) == 0 {
		return defaultValue
	}
	return value
}

func GetEnvNumeric(key string, defaultValue int) int {
	value := os.Getenv(key)
	if len(value) == 0 {
		return defaultValue
	}
	result, err := strconv.Atoi(value)
	if err != nil {
		return defaultValue
	}

	return result
}

func GetRequestBody(r *http.Request) []byte {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.ServiceLogger.Error("Error reading response body:", err)
		return nil
	}
	return body
}

func RestToJson(w http.ResponseWriter, rest any) (string, error) {
	w.Header().Set("Content-Type", "application/json")
	jsonData, err := json.Marshal(&rest)
	if err != nil {
		return "", err
	}
	logger.ServiceLogger.Trace(string(jsonData))
	return string(jsonData), nil
}

func UploadPatchXml(handler *multipart.FileHeader, err error, file multipart.File) (bool, error) {
	uploadDirectory := XMLDirectoryPath()
	if _, err := os.Stat(uploadDirectory); os.IsNotExist(err) {
		err := os.Mkdir(uploadDirectory, os.ModePerm)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	filePath := filepath.Join(uploadDirectory, "THIRD_PARTY_"+handler.Filename)
	_, err = os.Stat(filePath)
	if !os.IsNotExist(err) {
		err := os.Remove(filePath)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	newFile, err := os.Create(filePath)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error creating the file"))
		return false, err
	}

	_, err = file.Seek(0, io.SeekStart)
	if err != nil {
		return false, err
	}

	_, err = io.Copy(newFile, file)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error cpoying the file"))
		return false, err
	}
	defer func(newFile *os.File) {
		err = newFile.Close()
		if err != nil {
			logger.ServiceLogger.Error("[UploadPatchXml]", err.Error())
		}
	}(newFile)
	return true, nil
}

func FileDirectoryPath() string {
	currentDir, _ := os.Getwd()
	fileDbPath := filepath.Join(currentDir, "filedb")

	_, err := os.Stat(fileDbPath)
	if os.IsNotExist(err) {
		err := os.MkdirAll(fileDbPath, 0755)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	return GetEnv("FILE_DB_PATH", fileDbPath)
}

func XMLDirectoryPath() string {
	currentDir, _ := os.Getwd()
	xmlFolderPath := filepath.Join(currentDir, "filedb", "xmlfolder")

	_, err := os.Stat(xmlFolderPath)
	if os.IsNotExist(err) {
		err := os.MkdirAll(xmlFolderPath, 0755)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	return GetEnv("XML_DIR_PATH", xmlFolderPath)
}

func ThirdPartyXMLDirectoryPath() string {
	currentDir, _ := os.Getwd()
	xmlFolderPath := filepath.Join(currentDir, "filedb", "thirdpartyxmlfolder")

	_, err := os.Stat(xmlFolderPath)
	if os.IsNotExist(err) {
		err := os.MkdirAll(xmlFolderPath, 0755)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	return GetEnv("XML_DIR_PATH", xmlFolderPath)
}

func ThirdPartyTemplateXMLDirectoryPath() string {
	currentDir, _ := os.Getwd()
	xmlFolderPath := filepath.Join(currentDir, "filedb", "thirdpartytemplates")
	return xmlFolderPath
}

func ODTDirectoryPath() string {
	currentDir, _ := os.Getwd()
	xmlFolderPath := filepath.Join(currentDir, "filedb", "windowsodt")
	return xmlFolderPath
}

func LinuxDataDirectoryPath() string {
	currentDir, _ := os.Getwd()
	linuxDataPath := filepath.Join(currentDir, "filedb", "ubuntu-data")

	_, err := os.Stat(linuxDataPath)
	if os.IsNotExist(err) {
		err := os.MkdirAll(linuxDataPath, 0755)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	return GetEnv("LINUX_DATA_DIR_PATH", linuxDataPath)
}

func CentosDataDirectoryPath() string {
	currentDir, _ := os.Getwd()
	centosDataPath := filepath.Join(currentDir, "filedb", "centos-data")

	_, err := os.Stat(centosDataPath)
	if os.IsNotExist(err) {
		err := os.MkdirAll(centosDataPath, 0755)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	return GetEnv("CENTOS_DATA_DIR_PATH", centosDataPath)
}

func CabDataDirectoryPath() string {
	currentDir, _ := os.Getwd()
	linuxDataPath := filepath.Join(currentDir, "filedb", "cab-data")

	_, err := os.Stat(linuxDataPath)
	if os.IsNotExist(err) {
		err := os.MkdirAll(linuxDataPath, 0755)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	return GetEnv("CAB_DATA_DIR_PATH", linuxDataPath)
}

func MacDataDirectoryPath() string {
	currentDir, _ := os.Getwd()
	macDataPath := filepath.Join(currentDir, "filedb", "mac")
	_, err := os.Stat(macDataPath)
	if os.IsNotExist(err) {
		err := os.MkdirAll(macDataPath, 0755)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}
	return GetEnv("MAC_DATA_DIR_PATH", macDataPath)
}

func VulnerabilityDataDirectoryPath() string {
	currentDir, _ := os.Getwd()
	vulnerabilityDataPath := filepath.Join(currentDir, "filedb", "vulnerability")
	_, err := os.Stat(vulnerabilityDataPath)
	if os.IsNotExist(err) {
		err := os.MkdirAll(vulnerabilityDataPath, 0755)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}
	return GetEnv("VULNERABILITY_DATA_DIR_PATH", vulnerabilityDataPath)
}

func TempDirectoryPath() string {
	currentDir, _ := os.Getwd()
	path := filepath.Join(currentDir, "filedb", "tmp")
	_, err := os.Stat(path)
	if os.IsNotExist(err) {
		err := os.MkdirAll(path, 0755)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	return GetEnv("XML_DIR_PATH", path)
}

func CabTempDirectoryPath() string {
	currentDir, _ := os.Getwd()
	path := filepath.Join(currentDir, "filedb", "cab-tmp")
	_, err := os.Stat(path)
	if os.IsNotExist(err) {
		err := os.MkdirAll(path, 0755)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	return GetEnv("CAB_TMP_DIR_PATH", path)
}

func CurrentWorkingDir() string {
	WorkingDir, _ := os.Getwd()
	return WorkingDir
}

var matchFirstCap = regexp.MustCompile("(.)([A-Z][a-z]+)")
var matchAllCap = regexp.MustCompile("([a-z0-9])([A-Z])")

func ToSnakeCase(str string) string {
	snake := matchFirstCap.ReplaceAllString(str, "${1}_${2}")
	snake = matchAllCap.ReplaceAllString(snake, "${1}_${2}")
	return strings.ToLower(snake)
}

func AddInDiffMap(dbKey string, oldVal any, newVal any) map[string]map[string]interface{} {
	return map[string]map[string]interface{}{
		dbKey: {
			"oldvalue": oldVal,
			"newvalue": newVal,
		},
	}
}

func CompareVersions(version1, version2 string) int {
	components1 := strings.Split(version1, ".")
	components2 := strings.Split(version2, ".")

	for i := 0; i < len(components1) && i < len(components2); i++ {
		num1, _ := strconv.Atoi(components1[i])
		num2, _ := strconv.Atoi(components2[i])

		if num1 < num2 {
			return -1
		} else if num1 > num2 {
			return 1
		}
	}

	if len(components1) < len(components2) {
		return -1
	} else if len(components1) > len(components2) {
		return 1
	}

	return 0
}

func RemoveAlphabets(str string) string {
	var result []rune

	for _, char := range str {
		if !unicode.IsLetter(char) {
			result = append(result, char)
		}
	}

	return string(result)
}

func ConvertSizeToBytes(sizeString string) int64 {
	size := int64(0)
	sizeString = strings.ToLower(sizeString)
	if strings.HasSuffix(sizeString, "m") || strings.HasSuffix(sizeString, "mb") {
		sizeString = RemoveAlphabets(sizeString)
		megabytes, err := strconv.ParseFloat(sizeString, 64)
		if err != nil {
			return size
		}
		size = int64(megabytes * 1024 * 1024)
	} else {
		bytes, err := strconv.ParseFloat(sizeString, 64)
		if err != nil {
			return size
		}
		size = int64(bytes)
	}
	return size
}

func ParseOsTypeAndOsArch(os string, osType OsType, osArch OsArchitecture) (OsType, OsArchitecture) {
	if strings.Contains(os, "mac") {
		osType = MacOS
		osArch = All
	} else if strings.Contains(os, "win") {
		osType = Windows
		if strings.Contains(os, "win32") {
			osArch = X86
		} else if strings.Contains(os, "win64") {
			osArch = X64
		}
	} else if strings.Contains(os, "ubuntu") {
		osType = Ubuntu
		if strings.Contains(os, "ubuntu-i686") {
			osArch = X86
		} else if strings.Contains(os, "ubuntu-x86_64") {
			osArch = X64
		}
	} else if strings.Contains(os, "linux") {
		osType = Linux
		if strings.Contains(os, "linux-i686") {
			osArch = X86
		} else if strings.Contains(os, "linux-x86_64") {
			osArch = X64
		}
	}
	return osType, osArch
}

func GetKeyList(m map[string]bool) []string {
	keys := make([]string, 0, len(m))

	for key := range m {
		keys = append(keys, key)
	}

	return keys
}

func GetFileSizeFromUrl(urlPath string) int64 {
	resp, err := http.Head(urlPath)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting file size of url %s: %v\n", urlPath, err))
		return 0
	}
	defer func(Body io.ReadCloser) {
		err = Body.Close()
		if err != nil {
			logger.ServiceLogger.Error("[GetFileSizeFromUrl]", err.Error())
		}
	}(resp.Body)
	resp.Header.Get("Last-Modified")
	return resp.ContentLength
}

func GetHeadersFromUrl(urlPath string) http.Header {
	resp, err := http.Head(urlPath)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting file size of url %s: %v\n", urlPath, err))
		return nil
	}
	defer func(Body io.ReadCloser) {
		err = Body.Close()
		if err != nil {
			logger.ServiceLogger.Error("[GetHeadersFromUrl]", err.Error())
		}
	}(resp.Body)
	return resp.Header
}

func RemoveValue(slice []string, value string) []string {
	var result []string
	for _, v := range slice {
		if v != value {
			result = append(result, v)
		}
	}
	return result
}

func PartitionStringList(slice []string, chunkSize int) [][]string {
	var chunks [][]string
	for i := 0; i < len(slice); i += chunkSize {
		end := i + chunkSize
		if end > len(slice) {
			end = len(slice)
		}
		chunks = append(chunks, slice[i:end])
	}
	return chunks
}

func DownloadFileAndCopyToFilePath(url string, filePath, fileName string) bool {
	response, err := http.Get(url)
	if err != nil {
		logger.ServiceLogger.Error("Error:", err)
		return false
	}
	defer func(Body io.ReadCloser) {
		err = Body.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DownloadFileAndCopyToFilePath]", err.Error())
		}
	}(response.Body)

	_, err = os.Stat(filePath)
	if os.IsNotExist(err) {
		err := os.MkdirAll(filePath, 0755)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	_, err = os.Stat(filepath.Join(filePath, fileName))
	if !os.IsNotExist(err) {
		err := os.Remove(filepath.Join(filePath, fileName))
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	file, err := os.Create(filepath.Join(filePath, fileName))
	if err != nil {
		logger.ServiceLogger.Error("Error opening file:", err)
		return false
	}
	defer func(file *os.File) {
		err = file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DownloadFileAndCopyToFilePath]", err.Error())
		}
	}(file)

	_, err = io.Copy(file, response.Body)
	if err != nil {
		logger.ServiceLogger.Error("Error copying content to file:", err)
		return false
	}
	return true
}

func DecompressGzipFile(gzipFile string, newFile string) error {
	fis, err := os.Open(gzipFile)
	if err != nil {
		return err
	}
	defer func(fis *os.File) {
		err = fis.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DecompressGzipFile]", err.Error())
		}
	}(fis)

	gis, err := gzip.NewReader(fis)
	if err != nil {
		return err
	}
	defer func(gis *gzip.Reader) {
		err = gis.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DecompressGzipFile]", err.Error())
		}
	}(gis)

	_, err = os.Stat(newFile)
	if !os.IsNotExist(err) {
		err := os.Remove(newFile)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	fos, err := os.Create(newFile)
	if err != nil {
		return err
	}
	defer func(fos *os.File) {
		err = fos.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DecompressGzipFile]", err.Error())
		}
	}(fos)

	if _, err = io.Copy(fos, gis); err != nil {
		return err
	}
	return nil
}

func FilenameFromUrl(urlstr string) string {
	u, err := url.Parse(urlstr)
	if err != nil {
		log.Fatal("Error due to parsing url: ", err)
	}
	x, _ := url.QueryUnescape(u.EscapedPath())
	return filepath.Base(x)
}

func CreateZipFile(target string, source string) {
	if runtime.GOOS == "windows" {
		args := append([]string{"a", "-t7z", target}, source)
		err := exec.Command(`C:\Program Files\7-Zip\7z.exe`, args...).Run()
		if err != nil {
			log.Fatal(err)
			return
		}
	} else {
		args := append([]string{"a", target}, source)
		err := exec.Command("7z", args...).Run()
		if err != nil {
			log.Fatal(err)
			return
		}
	}
}

func ExecuteCommand(command string, args []string) string {
	cmd := exec.Command(command, args...)
	cmdOutput, _ := cmd.StdoutPipe()
	cmd.Stderr = cmd.Stdout // Redirect stderr to stdout
	// Start the command
	if err := cmd.Start(); err != nil {
		return "Error executing command:" + err.Error()
	}

	scanner := bufio.NewScanner(cmdOutput)
	for scanner.Scan() {
		logger.ServiceLogger.Debug(scanner.Text())
	}

	// Wait for the command to finish
	if err := cmd.Wait(); err != nil {
		return "Command finished with error:" + err.Error()
	} else {
		return ""
	}
}

func Unique[T comparable](s []T) []T {
	m := map[T]struct{}{}
	for _, v := range s {
		m[v] = struct{}{}
	}
	return maps.Keys(m)
}

func SanitizeJsonString(input string) string {
	// Replace all HTML tags with an empty string
	if len(input) > 0 {
		cleanText := htmlTagRegex.ReplaceAllString(input, "")
		jsonString, _ := json.Marshal(cleanText)
		return string(jsonString[1 : len(jsonString)-1])
	}
	return input
}

func RemoveDuplicates(fileDetails []model.FileData) []model.FileData {
	uniqueMap := make(map[string]bool)
	var result []model.FileData
	for _, file := range fileDetails {
		key := file.DownloadUrl + "::" + file.FileName // Unique key based on DownloadUrl and FileName
		if !uniqueMap[key] {
			uniqueMap[key] = true
			result = append(result, file)
		}
	}
	return result
}
