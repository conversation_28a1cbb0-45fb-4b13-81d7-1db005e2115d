package centos

import "patch-central-repo/model"

type CentosPatch struct {
	model.BaseEntityModel
	UUID              string `bun:"type:varchar(250)" json:"uuid"`
	OsVersion         string `bun:"type:varchar(50)" json:"os_version"`
	Channel           string `bun:"type:varchar(50)" json:"channel"`
	Repo              string `bun:"type:varchar(1000)" json:"repo"`
	PackageName       string `bun:"type:varchar(500)" json:"package_name"`
	Arch              string `bun:"type:varchar(50)" json:"architecture"`
	Version           string `bun:"type:varchar(250)" json:"version"`
	Release           string `bun:"type:varchar(100)" json:"release"`
	Epoch             string `bun:"type:varchar(50)" json:"epoch"`
	Summary           string `bun:"type:varchar(1000)" json:"summary"`
	Description       string `json:"description"`
	License           string `bun:"type:varchar(250)" json:"license"`
	Group             string `bun:"type:varchar(250)" json:"group"`
	Vendor            string `bun:"type:varchar(250)" json:"vendor"`
	Packager          string `bun:"type:varchar(250)" json:"packager"`
	BuildHost         string `bun:"type:varchar(250)" json:"build_host"`
	SourceRpm         string `bun:"type:varchar(500)" json:"source_rpm"`
	Requires          string `json:"requires"`
	Provides          string `json:"provides"`
	Conflicts         string `json:"conflicts"`
	Obsoletes         string `json:"obsoletes"`
	FileName          string `json:"file_name"`
	DownloadUrl       string `json:"download_url"`
	Size              int64  `json:"size"`
	InstalledSize     int64  `json:"installed_size"`
	Checksum          string `bun:"type:varchar(250)" json:"checksum"`
	ChecksumType      string `bun:"type:varchar(50)" json:"checksum_type"`
	PkgAndVersion     string `bun:"type:varchar(1000)" json:"pkg_and_version"`
	PkgNameWithDistro string `bun:"type:varchar(1000)" json:"pkg_name_with_distro"`
	Downloadable      bool   `json:"downloadable"`
	ReleaseDate       int64  `json:"release_date"`
	BuildTime         int64  `json:"build_time"`
}
