package centos

import "patch-central-repo/model"

type CentosNoticeData struct {
	model.BaseEntityModel
	CVEsIDs         []string `json:"cves_ids"`
	NoticeId        string   `bun:"type:varchar(100)" json:"id"`
	Description     string   `json:"description"`
	Instructions    string   `bun:"type:varchar(1000)" json:"instructions"`
	IsHidden        bool     `json:"is_hidden"`
	Published       string   `bun:"type:varchar(100)" json:"published"`
	Summary         string   `bun:"type:varchar(1000)" json:"summary"`
	Title           string   `bun:"type:varchar(250)" json:"title"`
	Type            string   `bun:"type:varchar(50)" json:"type"`
	AffectedOS      string   `bun:"type:varchar(250)" json:"affected_os"`
	SupportURL      string   `bun:"type:varchar(250)" json:"support_url"`
	ReleaseDate     int64
	ReleasePackages map[string][]CentosReleasePackage `json:"release_packages"`
}

type CentosReleasePackage struct {
	model.BaseEntityModel
	NoticeID       string `bun:"type:varchar(50)"`
	OSVersion      string `bun:"type:varchar(50)"`
	Description    string `bun:"type:varchar(500)"`
	IsSource       bool   `json:"is_source"`
	IsVisible      bool
	Repository     string `bun:"type:varchar(50)"`
	SourceLink     string `bun:"type:varchar(500)" json:"source_link"`
	Version        string `bun:"type:varchar(100)" json:"version"`
	VersionLink    string `bun:"type:varchar(500)" json:"version_link"`
	NameAndVersion string `bun:"type:varchar(250)"`
}
